import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { FileController } from '../controllers/FileController.js';
import { validateParams, validateQuery } from '../middleware/validation.js';
import { rateLimitPerUser } from '../middleware/auth.js';

const router = Router();

// Validation schemas
const fileIdSchema = Joi.object({
  fileId: Joi.string().required().min(1).max(255)
});

const downloadQuerySchema = Joi.object({
  download: Joi.string().valid('true', 'false').optional()
});

/**
 * Get secure file access URL or download file
 * GET /api/files/:fileId
 * Query params:
 * - download: 'true' to download file directly, 'false' to get access URL
 */
router.get('/:fileId',
  validateParams(fileIdSchema),
  validateQuery(downloadQuerySchema),
  rateLimitPerUser(100, 60 * 1000), // 100 file access requests per minute
  FileController.getFileAccess
);

/**
 * Get file metadata
 * GET /api/files/:fileId/metadata
 */
router.get('/:fileId/metadata',
  validateParams(fileIdSchema),
  rateLimitPerUser(50, 60 * 1000), // 50 metadata requests per minute
  FileController.getFileMetadata
);

export default router;
