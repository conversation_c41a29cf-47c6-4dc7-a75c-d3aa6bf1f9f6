import { asyncHand<PERSON> } from '../middleware/asyncHandler.js';
import { ResponseUtil } from '../utils/response.js';
import { S3Service } from '../services/S3Service.js';
import { ChatMessage } from '../models/chat/ChatMessage.js';
import { logger } from '../utils/logger.js';
import path from 'path';
import fs from 'fs/promises';

/**
 * File Controller
 * Handles secure file access and download operations
 */
class FileController {
  /**
   * Get secure file access URL
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getFileAccess = asyncHandler(async (req, res) => {
    const { fileId } = req.params;
    const { download = false } = req.query;

    if (!fileId) {
      ResponseUtil.badRequest(res, 'File ID is required');
      return;
    }

    try {
      // First, try to get file from secure mapping
      if (S3Service.isAvailable()) {
        try {
          const fileAccess = await S3Service.generatePresignedUrlBySecureId(
            fileId,
            3600 // 1 hour expiration
          );

          if (download === 'true') {
            // Redirect to presigned URL for download
            res.redirect(fileAccess.url);
            return;
          }

          ResponseUtil.success(res, 'File access URL generated successfully', {
            fileId,
            accessUrl: fileAccess.url,
            originalName: fileAccess.originalName,
            mimeType: fileAccess.mimeType,
            expiresIn: fileAccess.expiresIn,
            expiresAt: new Date(Date.now() + fileAccess.expiresIn * 1000).toISOString()
          });
          return;
        } catch (s3Error) {
          logger.warn(`S3 file access failed for ${fileId}:`, s3Error.message);
          // Fall through to database lookup
        }
      }

      // Fallback: Look up file in database by secure file ID
      const chatMessage = await ChatMessage.findOne({
        where: { attachmentSecureId: fileId }
      });

      if (!chatMessage) {
        ResponseUtil.notFound(res, 'File not found or access denied');
        return;
      }

      // If it's an S3 file, generate presigned URL
      if (chatMessage.attachmentStorageType === 's3' && chatMessage.attachmentS3Key) {
        if (!S3Service.isAvailable()) {
          ResponseUtil.serverError(res, 'File storage service is not available');
          return;
        }

        const presignedUrl = await S3Service.generatePresignedUrl(
          chatMessage.attachmentS3Key,
          3600 // 1 hour expiration
        );

        if (download === 'true') {
          // Redirect to presigned URL for download
          res.redirect(presignedUrl);
          return;
        }

        ResponseUtil.success(res, 'File access URL generated successfully', {
          fileId,
          accessUrl: presignedUrl,
          originalName: chatMessage.attachmentName,
          mimeType: chatMessage.attachmentType,
          expiresIn: 3600,
          expiresAt: new Date(Date.now() + 3600 * 1000).toISOString()
        });
        return;
      }

      // If it's a local file, serve it directly
      if (chatMessage.attachmentStorageType === 'local' && chatMessage.attachmentPath) {
        const filePath = path.join(process.cwd(), chatMessage.attachmentPath);
        
        try {
          await fs.access(filePath);
          
          if (download === 'true') {
            // Set headers for file download
            res.setHeader('Content-Disposition', `attachment; filename="${chatMessage.attachmentName}"`);
            res.setHeader('Content-Type', chatMessage.attachmentType || 'application/octet-stream');
            
            // Stream the file
            const fileBuffer = await fs.readFile(filePath);
            res.send(fileBuffer);
            return;
          }

          // For local files, we can't provide a direct URL, so we return file info
          ResponseUtil.success(res, 'Local file access available', {
            fileId,
            originalName: chatMessage.attachmentName,
            mimeType: chatMessage.attachmentType,
            storageType: 'local',
            message: 'Use download=true parameter to download the file'
          });
          return;
        } catch (fileError) {
          logger.error(`Local file not found: ${filePath}`, fileError);
          ResponseUtil.notFound(res, 'File not found on storage');
          return;
        }
      }

      ResponseUtil.notFound(res, 'File not found or invalid storage configuration');
    } catch (error) {
      logger.error('Error in file access:', error);
      ResponseUtil.serverError(res, 'Failed to access file');
    }
  });

  /**
   * Get file metadata
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static getFileMetadata = asyncHandler(async (req, res) => {
    const { fileId } = req.params;

    if (!fileId) {
      ResponseUtil.badRequest(res, 'File ID is required');
      return;
    }

    try {
      // First, try to get file from secure mapping
      if (S3Service.isAvailable()) {
        const fileMapping = S3Service.getSecureFileMapping(fileId);
        if (fileMapping) {
          ResponseUtil.success(res, 'File metadata retrieved successfully', {
            fileId,
            originalName: fileMapping.originalName,
            mimeType: fileMapping.mimeType,
            storageType: 's3',
            createdAt: fileMapping.createdAt
          });
          return;
        }
      }

      // Fallback: Look up file in database
      const chatMessage = await ChatMessage.findOne({
        where: { attachmentSecureId: fileId },
        attributes: [
          'attachmentName',
          'attachmentType',
          'attachmentSize',
          'attachmentStorageType',
          'createdAt'
        ]
      });

      if (!chatMessage) {
        ResponseUtil.notFound(res, 'File not found or access denied');
        return;
      }

      ResponseUtil.success(res, 'File metadata retrieved successfully', {
        fileId,
        originalName: chatMessage.attachmentName,
        mimeType: chatMessage.attachmentType,
        size: chatMessage.attachmentSize,
        storageType: chatMessage.attachmentStorageType,
        createdAt: chatMessage.createdAt
      });
    } catch (error) {
      logger.error('Error getting file metadata:', error);
      ResponseUtil.serverError(res, 'Failed to retrieve file metadata');
    }
  });
}

export { FileController };
